import { ProductStatus, Currency, ChargePolicy, DiscountType, DiscountLevel } from "@/lib/graphql/types/generated/graphql";

export interface ProductDimension {
    key: string;
    value: string;
}

export interface ProductFormData {
    productId?: string;
    name: string;
    productCode: string;
    description: string;
    status: ProductStatus;
    dimensions: ProductDimension[];
    pricing: {
        id?: string;
        chargePolicy: ChargePolicy;
        costPrice: {
            value: number;
            currency: Currency;
        };
        listPrice: {
            value: number;
            currency: Currency;
        };
        sellingPrice: {
            value: number;
            currency: Currency;
        };
        unit: {
            unit: number;
            unitType: string;
        };
        discounts: {
            discountType: DiscountType;
            discountLevel: DiscountLevel;
            discountValue: {
                amount: {
                    value: number;
                    currency: Currency;
                };
                percentage: number;
            };
        }[];
        margin: {
            absoluteAmount: {
                value: number;
                currency: Currency;
            };
            percentage: number;
        };
    };
}

export interface ProductUpsertInput {
    id?: string;
    productCode: string;
    name: string;
    description: string;
    dimensions: ProductDimension[];
    pricing: {
        chargePolicy: ChargePolicy;
        costPrice: {
            value: number;
            currency: Currency;
        };
        listPrice: {
            value: number;
            currency: Currency;
        };
        sellingPrice: {
            value: number;
            currency: Currency;
        };
        productUnit: {
            unit: number;
            unitType: string;
        };
        discounts: {
            discountType: DiscountType;
            discountLevel: DiscountLevel;
            discountValue: {
                amount: {
                    value: number;
                    currency: Currency;
                };
                percentage: number;
            };
        }[];
        margin: {
            absoluteAmount: {
                value: number;
                currency: Currency;
            };
            percentage: number;
        };
    };
} 