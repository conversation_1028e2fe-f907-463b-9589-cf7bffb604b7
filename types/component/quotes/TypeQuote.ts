import { CustomerStatus, CustomerType, CompanyUser } from '@/lib/graphql/types/generated/graphql';

// Quote status enum - will be replaced with GraphQL enum later
export enum QuoteStatus {
    DRAFT = 'DRAFT',
    PENDING = 'PENDING',
    APPROVED = 'APPROVED',
    REJECTED = 'REJECTED',
    EXPIRED = 'EXPIRED'
}

// Interface for quote data structure
export interface QuoteWithBasicDetails {
    id: string;
    quoteNumber: string;
    customerName: string;
    customerType: CustomerType;
    customerStatus: CustomerStatus;
    salesExecutive: string;
    salesExecutiveId: string;
    totalValue: number;
    currency: string;
    status: QuoteStatus;
    createdDate: string;
    expiryDate: string;
    lastModified: string;
}

// Props for quote controls component
export interface QuotesControlsProps {
    searchQuery: string;
    onSearch: (e: React.ChangeEvent<HTMLInputElement>) => void;
    onAddQuote: () => void;
}

// Props for quote table headers
export interface QuoteTableHeader {
    label: string;
    key?: string;
    pill: boolean;
    pillColor: string;
    bold: boolean;
    size: string;
    cellClass: string;
    pillClass: string;
}
