import { gql } from "@apollo/client";


export const GET_ALL_PRODUCTS = gql`
    query GetAllProducts($status: ProductStatus) {
        masterProductsGet(
            filters: {
                status: $status
            }
        ) {
            id
            name
            description
            status
            productCode
            dimensions {
                key
                value
            }
            pricing {
                id
                chargePolicy
                costPrice {
                    value
                    currency
                }
                listPrice {
                    value
                    currency
                }
                sellingPrice {
                    value
                    currency
                }
                unit {
                    unit
                    unitType
                }
                discount {
                    id
                    discountType
                    discountValue {
                        value {
                            value
                            currency
                        }
                        percentage
                    }
                }
            }
        }
    }
`;

export const GET_PRODUCT_BY_ID = gql`
    query GetProductById($id: ID!) {
        masterProductGetById(
            id: $id
        ) {
            id
            name
            description
            status
            productCode
            dimensions {
                key
                value
            }
            pricing {
                id
                chargePolicy
                costPrice {
                    value
                    currency
                }
                listPrice {
                    value
                    currency
                }
                sellingPrice {
                    value
                    currency
                }
                unit {
                    unit
                    unitType
                }
                discount {
                    id
                    discountType
                    discountValue {
                        value {
                            value
                            currency
                        }
                        percentage
                    }
                }
            }
        }
    }
`;