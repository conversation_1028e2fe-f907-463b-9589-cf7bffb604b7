'use client';

import React, { useEffect } from 'react';
import AIInsightsPanel from '@/component/ai/ComponentAIInsights';
import ComponentBusinessMetrics from '@/component/dashboard/ComponentBusinessMetrics';
import ComponentTaskChecklist from '@/component/dashboard/ComponentTaskChecklist';
import { useCompanyContext } from '@/component/auth/ComponentRouteProtection';

export default function HomePage() {

    const { hasCompany, retry } = useCompanyContext();

    useEffect(() => {
        if (!hasCompany) {
            retry();
        }
    }, [hasCompany, retry]);

    return (
        <div className="container mx-auto max-w-5xl px-4 py-8">
            <div className="max-w-2xl mx-auto mb-8">
                <AIInsightsPanel />
            </div>

            <h1 className="text-3xl font-bold">Dashboard</h1>
            <div className="text-lg text-gray-500 mb-8">Welcome to One Platform!</div>

            <div className="grid grid-cols-1 gap-6">
                {/* Business Metrics Widget */}
                <div className="col-span-1">
                    <ComponentBusinessMetrics />
                </div>

                {/* Task Checklist Widget */}
                <div className="col-span-1">
                    <ComponentTaskChecklist />
                </div>
            </div>
        </div>
    );
}
