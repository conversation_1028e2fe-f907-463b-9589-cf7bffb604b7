'use client';

import { getUserFriendlyErrorMessage } from "@/lib/graphql";
import { useCompanyUserActivateMutation } from "@/lib/graphql/types/generated/hooks";
import { ApolloError } from "@apollo/client";
import { useRouter } from "next/navigation";
import { useState, useEffect } from "react";

const CompanyUserActivate = () => {
    const router = useRouter();
    const [companyUserActivate] = useCompanyUserActivateMutation();
    const [error, setError] = useState<string | null>(null);

    useEffect(() => {
        companyUserActivate({
            onCompleted: (data) => {
                if (data?.companyUserActivate) {
                    const redirectUrl = new URLSearchParams(window.location.search).get('redirectUrl');
                    // Only redirect if redirectUrl is provided
                    if (redirectUrl) {
                        router.replace(redirectUrl);
                    } else {
                        router.replace('/');
                    }
                }
            },
            onError: (error: ApolloError) => {
                setError(getUserFriendlyErrorMessage(error));
            }
        });
    }, []);


    return <>
        {!error && <div className="flex justify-center items-center h-screen">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white mr-1"></div>
        </div>}

        {error && (
            <div className="mb-6 bg-red-50 border border-red-200 rounded-lg p-4">
                <p className="text-sm text-red-800">{error}</p>
            </div>
        )}
    </>
}

export default CompanyUserActivate;


// this is an activate route
// prerequisites (will automatically happen as part of the process)
// needs user to login via google,
// needs user to have been associated to a company (verification provider)
// component with loading screen only, activate and redirect to home
// if error show error screen
