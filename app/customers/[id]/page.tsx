'use client';

import React from 'react';
import ComponentCustomerDetailLayout from '@/component/customers/customerdetails/ComponentCustomerDetailLayout';
import ComponentCustomerBasicDetails from '@/component/customers/customerdetails/ComponentCustomerBasicDetails';
import ComponentCustomerInsights from '@/component/customers/customerdetails/ComponentCustomerInsights';
import ComponentCustomerNotes from '@/component/customers/customerdetails/ComponentCustomerNotes';
import ComponentCustomerAssignments from '@/component/customers/customerdetails/ComponentCustomerAssignments';
import { useParams } from 'next/navigation';

const mockInsights = {
    lifetimeValue: 12000,
    avgInvoice: 400,
    totalVisits: 30,
};

const CustomerOverviewPage = () => {
    const params = useParams();
    const customerId = params.id as string;

    return (
        <ComponentCustomerDetailLayout>
            <div className="">
                <div className="mb-8">
                    <h2 className="text-2xl font-bold text-gray-900 mb-2">Customer Overview</h2>
                    <p className="text-gray-600">Complete customer profile and key insights</p>
                </div>
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <ComponentCustomerBasicDetails customerId={customerId} />
                    <ComponentCustomerInsights {...mockInsights} />
                    <ComponentCustomerNotes customerId={customerId} />
                    <ComponentCustomerAssignments customerId={customerId} />
                    {/* <ComponentCustomerDocuments customerId={customerId} /> */}
                </div>
            </div>
        </ComponentCustomerDetailLayout>
    );
};

export default CustomerOverviewPage;