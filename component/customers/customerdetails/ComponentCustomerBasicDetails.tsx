import React from 'react';
import { Mail, Phone, Building, MapPin, Calendar, Activity, Edit, Globe, Briefcase } from 'lucide-react';
import { useRouter } from 'next/navigation';

import { CustomerBusiness, CustomerIndividual, getUserFriendlyErrorMessage } from '@/lib/graphql';
import { useGetCustomerByIdQuery } from '@/lib/graphql/types/generated/hooks';
import { CustomerStatus } from '@/lib/graphql/types/generated/graphql';
import ComponentLoading from '@/component/common/ComponentLoading';

interface CustomerBasicDetailsProps {
    customerId: string;
}

const DetailItem = ({ icon: Icon, label, value }: { icon: React.ElementType, label: string, value: string | undefined }) => {
    if (!value) return null;
    return (
        <div className="flex items-start gap-4 p-4 bg-gray-50 rounded-lg border border-gray-100">
            <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center flex-shrink-0">
                <Icon className="w-4 h-4 text-blue-600" />
            </div>
            <div className="flex-1">
                <p className="text-sm font-medium text-gray-700 mb-1">{label}</p>
                <p className="text-gray-900 font-semibold">{value}</p>
            </div>
        </div>
    );
};

const ComponentCustomerBasicDetails: React.FC<CustomerBasicDetailsProps> = ({ customerId }) => {
    const router = useRouter();
    const { data: customerData, loading: isLoading, error } = useGetCustomerByIdQuery({
        variables: { id: customerId },
    });

    if (isLoading) {
        return (
            <div className="bg-white rounded-lg border border-gray-200 w-full p-6">
                <ComponentLoading
                    message="Loading customer details..."
                    className="min-h-[200px]"
                />
            </div>
        );
    }

    if (error) {
        return (
            <div className="bg-white rounded-lg  border border-gray-200 w-full p-6">
                <div className="text-red-600">
                    {getUserFriendlyErrorMessage(error)}
                </div>
            </div>
        );
    }

    const customer = customerData?.getCustomer as CustomerBusiness | CustomerIndividual;
    if (!customer) return null;

    let name = '';
    let email = '';
    let phone = '';
    let type = '';
    let website = '';
    let industry = '';

    if (customer.__typename === 'CustomerBusiness') {
        const contactDetails = customer.basicDetails.contactDetails;
        name = customer.basicDetails.legalName;
        email = contactDetails.email;
        phone = contactDetails.phoneNo || '';
        type = 'Business';
        website = customer.basicDetails.website;
        industry = customer.basicDetails.industry || '';
    } else if (customer.__typename === 'CustomerIndividual') {
        const contactDetails = customer.basicDetails.contactDetails;
        name = contactDetails.name;
        email = contactDetails.email;
        phone = contactDetails.phoneNo || '';
        type = 'Individual';
    }

    const statusColorMap: { [key: string]: string } = {
        [CustomerStatus.Active]: 'bg-green-100 text-green-800',
        [CustomerStatus.Suspended]: 'bg-red-100 text-red-800',
    };

    const handleEditClick = () => {
        router.push(`/customers/new?mode=edit&id=${customerId}`);
    };

    return (
        <div className="bg-white rounded-lg border border-gray-200 w-full">
            {/* Header with Avatar and Name */}
            <div className="p-6 border-b border-gray-200 flex items-center gap-4">
                <div className="relative">
                    <img
                        src={`https://ui-avatars.com/api/?name=${name.replace(' ', '+')}&background=3B5BA5&color=fff`}
                        alt={name}
                        className="w-16 h-16 rounded-full border-2 border-gray-200"
                    />
                    <div className={`absolute -bottom-1 -right-1 w-5 h-5 ${customer.status === CustomerStatus.Active ? 'bg-green-500' : 'bg-red-500'} border-2 border-white rounded-full`}></div>
                </div>
                <div className="flex-1">
                    <h3 className="text-xl font-bold text-gray-900">{name}</h3>
                    <p className="text-sm text-gray-600 flex items-center gap-1">
                        <Mail className="w-4 h-4" />
                        {email}
                    </p>
                </div>
                <button
                    onClick={handleEditClick}
                    className="p-2 rounded-lg hover:bg-gray-100 text-gray-500 hover:text-primary transition-colors"
                >
                    <Edit className="w-5 h-5" />
                </button>
            </div>

            {/* Details Section */}
            <div className="p-6">
                <div className="grid grid-cols-1 gap-4">
                    <DetailItem icon={Building} label="Type" value={type} />
                    <DetailItem icon={Phone} label="Phone" value={phone} />
                    {website && <DetailItem icon={Globe} label="Website" value={website} />}
                    {customer.stage && <DetailItem icon={Briefcase} label="Stage" value={customer.stage} />}
                    <div className="flex items-start gap-4 p-4 bg-gray-50 rounded-lg border border-gray-100">
                        <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center flex-shrink-0">
                            <Activity className="w-4 h-4 text-blue-600" />
                        </div>
                        <div className="flex-1">
                            <p className="text-sm font-medium text-gray-700 mb-1">Status</p>
                            <span className={`px-3 -mx-1 py-1 rounded-full text-sm font-semibold ${statusColorMap[customer.status] || 'bg-gray-100 text-gray-800'}`}>
                                {customer.status}
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default ComponentCustomerBasicDetails;
