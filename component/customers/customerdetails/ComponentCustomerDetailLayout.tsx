'use client';

import React from 'react';
import { ArrowLeft, User, Eye, FileText, Handshake, Activity } from 'lucide-react';
import { useRouter, useParams } from 'next/navigation';
import SecondaryNavbar, { SecondaryNavbarLayout } from '../../common/ComponentSecondaryNavbar';
import { SecondaryNavItem } from '@/types/component/common/TypeSecondaryNavbar';

// Mock customer data - in a real app, this would be fetched based on the ID
const mockCustomer = {
    id: 1,
    name: '<PERSON>',
    email: '<EMAIL>',
    company: 'Tech Corp',
    status: 'Active',
    lastContact: '2024-02-15',
    phone: '+1234567890',
    address: '123 Main St, Springfield, USA',
    avatar: 'https://i.pravatar.cc/150?u=a042581f4e29026704d',
};

interface CustomerDetailLayoutProps {
    children: React.ReactNode;
}

const ComponentCustomerDetailLayout: React.FC<CustomerDetailLayoutProps> = ({ children }) => {
    const router = useRouter();
    const params = useParams();
    const customerId = params.id as string;

    const navItems: SecondaryNavItem[] = [
        {
            id: 'overview',
            label: 'Overview',
            icon: <Eye className="h-5 w-5" />,
            href: `/customers/${customerId}`,
        },
        {
            id: 'invoices',
            label: 'Invoices',
            icon: <FileText className="h-5 w-5" />,
            href: `/customers/${customerId}/invoices`,
        },
        {
            id: 'services',
            label: 'Services',
            icon: <Handshake className="h-5 w-5" />,
            href: `/customers/${customerId}/services`,
        },
        {
            id: 'activities',
            label: 'Activities',
            icon: <Activity className="h-5 w-5" />,
            href: `/customers/${customerId}/activities`,
        },
    ];

    return (
        <div className="max-w-5xl m-auto">
            {/* Header Section */}
            <div className="px-25 py-6">
                <button
                    onClick={() => router.push('/customers')}
                    className="mb-4 flex items-center text-gray-600 hover:text-secondary transition-colors h-10 rounded-md"
                >
                    <ArrowLeft className="h-5 w-5 mr-2" />
                    Back to Customers
                </button>
            </div>
            <div className='mb-10 min-h-screen'>
                <SecondaryNavbarLayout
                    navbar={
                        <SecondaryNavbar
                            items={navItems}
                        />
                    }
                    content={children}
                />
            </div>
        </div>

    );
};

export default ComponentCustomerDetailLayout;
