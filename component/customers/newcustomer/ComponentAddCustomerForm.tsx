import React, { useState, useEffect } from 'react';
import { ArrowLeft, User, Briefcase, UserPlus, FileText, MessageSquare, Users, Link } from 'lucide-react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useForm, FormProvider } from 'react-hook-form';
import AIInsightsPanel from '@/component/ai/ComponentAIInsights';
import BasicDetailsSection from './basicdetails/ComponentBasicDetailsSection';
import DocumentsSection from './documents/ComponentDocumentsSection';
import NotesSection from './notes/ComponentNotesSection';
import TeamAssignmentSection from './assignment/ComponentTeamAssignmentSection';
import { CustomerType, CustomerStage, ContactType, EntitySize, CustomerUpsertBasicDetailsInput, CustomerUpsertAdditionalDetailsInput } from '@/lib/graphql/types/generated/graphql';
import { getUserFriendlyErrorMessage } from '@/lib/graphql/utils/errorHandling';
import { ApolloError } from '@apollo/client';
import ComponentNote from '@/component/common/ComponentNote';
import { useCustomerUpsertAdditionalDetailsMutation, useGetCustomerByIdQuery, useCustomerUpsertBasicDetailsMutation } from '@/lib/graphql/types/generated/hooks';
import { AddCustomerFormData } from '@/types/component/customers/newcustomer/TypeAddCustomerForm';


const AddCustomerForm: React.FC = () => {
    const router = useRouter();
    const searchParams = useSearchParams();
    const isEditMode = searchParams.get('mode') === 'edit';
    const customerId = searchParams.get('id');

    const [customerTypeTab, setCustomerTypeTab] = useState<CustomerType>(CustomerType.Individual);
    const [customerUpsertBasicDetails] = useCustomerUpsertBasicDetailsMutation();
    const [customerUpsertAdditionalDetails] = useCustomerUpsertAdditionalDetailsMutation();

    const [isLoading, setIsLoading] = useState(false);
    const [basicDetailsNote, setBasicDetailsNote] = useState<React.ReactNode | null>(null);

    const { data: customerData, refetch: refetchCustomerData } = useGetCustomerByIdQuery({
        variables: { id: customerId || '' },
        skip: !isEditMode || !customerId,
    });


    const methods = useForm<AddCustomerFormData>({
        defaultValues: {
            type: CustomerType.Individual,
            stage: CustomerStage.Lead,
            name: '',
            email: '',
            phone: '',
            referralSource: '',
            customFields: [],
            companyLegalName: '',
            companyWebsite: '',
            companySize: EntitySize.LessThanTen,
            industry: '',
            contactName: '',
            contactTitle: '',
            contactEmail: '',
            contactPhone: '',
            contactType: ContactType.Business,
            notes: [],
            newNotes: {
                content: '',
                tags: [],
            },
            assignments: {
                accountManager: '',
                supportRepresentative: ''
            },
        },
    });

    // Load existing customer data when in edit mode
    useEffect(() => {
        if (isEditMode && customerData?.getCustomer) {
            const customer = customerData.getCustomer;
            const isBusinessCustomer = customer.__typename === 'CustomerBusiness';

            setCustomerTypeTab(isBusinessCustomer ? CustomerType.Business : CustomerType.Individual);

            if (isBusinessCustomer) {
                const businessCustomer = customer;
                methods.reset({
                    customerId: customer.id,
                    type: CustomerType.Business,
                    stage: customer.stage,
                    companyLegalName: businessCustomer.basicDetails.legalName || '',
                    companyWebsite: businessCustomer.basicDetails.website || '',
                    companySize: businessCustomer.basicDetails.size || EntitySize.LessThanTen,
                    industry: businessCustomer.basicDetails.industry || '',
                    contactName: businessCustomer.basicDetails.contactDetails.name,
                    contactTitle: businessCustomer.basicDetails.contactDetails.title || '',
                    contactEmail: businessCustomer.basicDetails.contactDetails.email,
                    contactPhone: businessCustomer.basicDetails.contactDetails.phoneNo || '',
                    contactType: businessCustomer.basicDetails.contactDetails.contactType,
                    referralSource: businessCustomer.basicDetails.referralSource || '',
                    notes: customer.notes?.map((note) => ({ content: note?.content || '', tags: note?.tags || [] })) as { content: string; tags: string[] }[] || [{ content: '', tags: [] }],
                    newNotes: {
                        content: '',
                        tags: [],
                    },
                    assignments: customer.assignments?.[0]
                        ? {
                            accountManager: customer.assignments[0].accountManager?.id || '',
                            supportRepresentative: customer.assignments[0].supportRepresentative?.id || ''
                        }
                        : {
                            accountManager: '',
                            supportRepresentative: ''
                        },
                    customFields: customer.customTags?.map((tag) => ({
                        label: tag?.label || '',
                        key: tag?.key || '',
                        dataType: tag?.type || '',
                        description: tag?.description || '',
                        value: tag?.value || '',
                    })) || [],
                    name: '',
                    email: '',
                    phone: '',
                });
            } else {
                const individualCustomer = customer;
                methods.reset({
                    customerId: customer.id,
                    type: CustomerType.Individual,
                    stage: customer.stage,
                    name: individualCustomer.basicDetails.contactDetails.name,
                    email: individualCustomer.basicDetails.contactDetails.email,
                    phone: individualCustomer.basicDetails.contactDetails.phoneNo || '',
                    referralSource: individualCustomer.basicDetails.referralSource || '',
                    notes: customer.notes?.map((note) => ({ content: note?.content || '', tags: note?.tags || [] })) as { content: string; tags: string[] }[] || [{ content: '', tags: [] }],
                    newNotes: {
                        content: '',
                        tags: [],
                    },
                    assignments: customer.assignments?.[0]
                        ? {
                            accountManager: customer.assignments[0].accountManager?.id || '',
                            supportRepresentative: customer.assignments[0].supportRepresentative?.id || ''
                        }
                        : {
                            accountManager: '',
                            supportRepresentative: ''
                        },
                    customFields: customer.customTags?.map((tag) => ({
                        label: tag?.label || '',
                        key: tag?.key || '',
                        dataType: tag?.type || '',
                        description: tag?.description || '',
                        value: tag?.value || '',
                    })) || [],
                    companyLegalName: '',
                    companyWebsite: '',
                    companySize: EntitySize.LessThanTen,
                    industry: '',
                    contactName: '',
                    contactTitle: '',
                    contactEmail: '',
                    contactPhone: '',
                    contactType: ContactType.Person,
                });
            }
        }
    }, [isEditMode, customerData, methods]);

    const mapToCustomerUpsertBasicDetailsInput = (data: any): CustomerUpsertBasicDetailsInput => {
        if (customerTypeTab === CustomerType.Individual) {
            return {
                customerId: data.customerId,
                customerType: CustomerType.Individual,
                customerStage: data.stage || CustomerStage.Lead,
                individualCustomerDetails: {
                    fullName: data.name,
                    emailAddress: data.email,
                    phoneNumber: data.phone,
                    referralSource: data.referralSource,
                    address: '',
                }
            };
        } else {
            return {
                customerId: data.customerId,
                customerType: CustomerType.Business,
                customerStage: data.stage || CustomerStage.Lead,
                businessCustomerDetails: {
                    legalName: data.companyLegalName,
                    website: data.companyWebsite,
                    size: data.companySize,
                    industry: data.industry,
                    referralSource: data.referralSource,
                    address: '',
                    contactPersonDetails: {
                        name: data.contactName,
                        title: data.contactTitle,
                        email: data.contactEmail,
                        phoneNo: data.contactPhone,
                        contactType: data.contactType || ContactType.Business,
                    },
                }
            };
        }
    };

    const mapToCustomerUpsertAdditionalDetailsInput = (data: any): CustomerUpsertAdditionalDetailsInput => {
        // no deletion of notes allowed anyway, so only sending the new notes to add
        const newNotesToAdd = data.newNotes.content ? [{ content: data.newNotes.content, tags: data.newNotes.tags }] : [];
        return {
            customerId: data.customerId,
            documents: data.documents,
            notes: [...newNotesToAdd],
            assignments: data.assignments
        };
    };

    const handleSaveBasicDetails = async (data: any) => {
        setIsLoading(true);

        try {
            await customerUpsertBasicDetails({
                variables: {
                    input: mapToCustomerUpsertBasicDetailsInput(data)
                },
                onCompleted: async (data) => {
                    if (data.customerUpsertBasicDetails) {
                        methods.setValue('customerId', data.customerUpsertBasicDetails.id);
                        setIsLoading(false);
                        setBasicDetailsNote(<ComponentNote>Basic details are saved successfully..</ComponentNote>);
                        router.push(`/customers/new?mode=edit&id=${data.customerUpsertBasicDetails.id}`);
                        // Refetch customer data after successful save
                        if (isEditMode && customerId) {
                            await refetchCustomerData();
                        }
                    }
                },
                onError: (error) => {
                    setIsLoading(false);
                    setBasicDetailsNote(<ComponentNote isError={true}>{getUserFriendlyErrorMessage(error as ApolloError)}</ComponentNote>);
                }
            });
        } finally {
            setIsLoading(false);
        }
    };

    const handleSaveAdditionalDetails = async (data: any) => {
        setIsLoading(true);

        try {
            await customerUpsertAdditionalDetails({
                variables: {
                    input: mapToCustomerUpsertAdditionalDetailsInput(data)
                },
                onCompleted: async (data) => {
                    if (data.customerUpsertAdditionalDetails) {
                        setIsLoading(false);
                        setBasicDetailsNote(<ComponentNote>Additional details are saved successfully.. </ComponentNote>);
                        router.push(`/customers/new?mode=edit&id=${data.customerUpsertAdditionalDetails.id}`);
                        // Refetch customer data after successful save
                        if (isEditMode && customerId) {
                            await refetchCustomerData();
                        }
                    }
                },
                onError: (error) => {
                    setIsLoading(false);
                    setBasicDetailsNote(<ComponentNote isError={true}>{getUserFriendlyErrorMessage(error as ApolloError)}</ComponentNote>);
                }
            });
        } finally {
            setIsLoading(false);
        }
    };

    return (
        <div className="min-h-screen">
            <div className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
                {/* AI Insights Panel */}
                <div className="mb-8 max-w-2xl mx-auto">
                    <AIInsightsPanel />
                </div>

                {/* Header */}
                <div className="mb-8">
                    <button
                        onClick={() => router.back()}
                        className="mb-6 flex items-center text-gray-600 hover:text-secondary transition-colors"
                    >
                        <ArrowLeft className="h-5 w-5 mr-2" />
                        Back
                    </button>

                    <div className="text-center mt-10">
                        {(
                            <div className="flex justify-center mb-8">
                                <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg">
                                    <button
                                        type="button"
                                        disabled={isEditMode}
                                        onClick={() => setCustomerTypeTab(CustomerType.Individual)}
                                        className={`flex items-center justify-center px-6 py-3 rounded-md text-sm font-medium transition-colors ${customerTypeTab === CustomerType.Individual
                                            ? 'bg-secondary text-white'
                                            : 'text-gray-600 hover:text-gray-900 disabled:opacity-50 disabled:cursor-not-allowed'
                                            }`}
                                    >
                                        <User className="h-5 w-5 mr-2" />
                                        Individual
                                    </button>
                                    <button
                                        type="button"
                                        disabled={isEditMode}
                                        onClick={() => setCustomerTypeTab(CustomerType.Business)}
                                        className={`flex items-center justify-center px-6 py-3 rounded-md text-sm font-medium transition-colors ${customerTypeTab === CustomerType.Business
                                            ? 'bg-secondary text-white'
                                            : 'text-gray-600 hover:text-gray-900 disabled:opacity-50 disabled:cursor-not-allowed'
                                            }`}
                                    >
                                        <Briefcase className="h-5 w-5 mr-2" />
                                        Business
                                    </button>
                                </div>
                            </div>
                        )}
                    </div>
                </div>

                {/* Form Content */}
                <FormProvider {...methods}>
                    <form onSubmit={methods.handleSubmit(handleSaveBasicDetails)} className="space-y-8">
                        <div className="mb-6">
                            <h2 className="text-xl font-bold text-gray-900 mb-2">Basic Details</h2>
                            <p className="text-gray-600">Enter the customer's primary information and contact details</p>
                        </div>
                        {basicDetailsNote && (
                            <div className="mb-6">
                                {basicDetailsNote}
                            </div>
                        )}
                        <div className="bg-white rounded-lg  border border-gray-200 p-8 mt-10">
                            <BasicDetailsSection customerTypeTab={customerTypeTab} />
                            <div className="flex justify-end items-center gap-2 mt-10">
                                <button
                                    type="submit"
                                    disabled={isLoading}
                                    className="px-6 py-2 bg-primary text-white rounded-lg hover:bg-opacity-90 transition-colors disabled:opacity-50"
                                >
                                    {isLoading
                                        ? 'Saving...'
                                        : 'Save Details'
                                    }
                                </button>
                            </div>
                        </div>
                    </form>

                    {
                        !methods.getValues('customerId') && (
                            <div className="mb-6">
                                <div className="mb-6 mt-10">
                                    <h2 className="text-xl font-bold text-gray-900 mb-2">Additional Information</h2>
                                    <p className="text-gray-600">Configure documents, notes, and team assignments for better customer management</p>
                                </div>
                                <div className="">
                                    <ComponentNote isError={true}> Customer basic details need to be saved first, before additional details can be added for the customer!</ComponentNote>
                                </div>
                            </div>
                        )
                    }

                    {methods.getValues('customerId') && (
                        <form onSubmit={methods.handleSubmit(handleSaveAdditionalDetails)} className="space-y-8">
                            <div className="mb-6 mt-10">
                                <h2 className="text-xl font-bold text-gray-900 mb-2">Additional Information</h2>
                                <p className="text-gray-600">Configure documents, notes, and team assignments for better customer management</p>
                            </div>
                            {basicDetailsNote && (
                                <div className="mb-8">
                                    {basicDetailsNote}
                                </div>
                            )}
                            <div className="bg-white rounded-lg  border border-gray-200 p-8">
                                <div className="mb-8">
                                    <div className="flex items-center mb-4">
                                        <div>
                                            <h3 className="text-lg font-semibold text-gray-900">Documents</h3>
                                            {/* <p className="text-sm text-gray-600">Upload customer documents and files</p> */}
                                        </div>
                                    </div>
                                    <DocumentsSection />
                                </div>

                                <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                                    <div>
                                        <div className="flex items-center mb-4">
                                            <div>
                                                <h3 className="text-lg font-semibold text-gray-900">Notes</h3>
                                                {/* <p className="text-sm text-gray-600">Add internal notes about the customer</p> */}
                                            </div>
                                        </div>
                                        <NotesSection />
                                    </div>

                                    <div>
                                        <div className="flex items-center mb-4">
                                            <div>
                                                <h3 className="text-lg font-semibold text-gray-900">Team Assignment</h3>
                                                {/* <p className="text-sm text-gray-600">Assign team members to this customer</p> */}
                                            </div>
                                        </div>
                                        <TeamAssignmentSection />
                                    </div>
                                </div>

                                <div className="flex justify-end items-center gap-2 mt-10">
                                    <button
                                        type="submit"
                                        disabled={isLoading}
                                        className="px-6 py-2 bg-primary text-white rounded-lg hover:bg-opacity-90 transition-colors disabled:opacity-50"
                                    >
                                        {isLoading
                                            ? 'Saving...'
                                            : 'Save Details'
                                        }
                                    </button>
                                </div>
                            </div>
                        </form>
                    )}
                </FormProvider>
            </div>
        </div >
    );
};

export default AddCustomerForm;