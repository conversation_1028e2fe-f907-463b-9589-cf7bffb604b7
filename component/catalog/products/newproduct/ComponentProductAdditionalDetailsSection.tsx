import React, { useState } from 'react';
import ComponentDocumentUpload, { DocumentInput } from '@/component/common/ComponentDocumentUpload';
import { ComponentCustomTagsInput } from '@/component/common/ComponentCustomTagInput';
import { CustomTagInput } from '@/component/common/ComponentCustomTagInput';
import { SaveIcon } from 'lucide-react';

const ComponentProductAdditionalDetailsSection: React.FC = () => {
    const [documents, setDocuments] = useState<DocumentInput[]>([]);
    const [customTags, setCustomTags] = useState<CustomTagInput[]>([]); // Explicitly typed

    return (
        <div className="mt-12">
            <div className="mb-4">
                <h2 className="text-2xl font-bold text-gray-900">Additional Details</h2>
                <p className="text-sm text-gray-600">Attach documents and add custom tags to enrich your product information.</p>
            </div>
            <div className="bg-white rounded-lg border border-gray-200 p-6 flex flex-col space-y-8">
                <ComponentDocumentUpload value={documents} onDocumentsChange={setDocuments} />
                <ComponentCustomTagsInput value={customTags} onTagsChange={setCustomTags} />
                <div className="flex justify-end col-span-2 mx-5">
                    <button type="submit" className="px-6 py-2 bg-primary text-white rounded-lg hover:bg-opacity-90 cursor-pointer">
                        <div className='flex items-center gap-2'><SaveIcon className='h-4 w-4' /> Save Additional Details</div>
                    </button>
                </div>
            </div>
        </div >
    );
};

export default ComponentProductAdditionalDetailsSection; 