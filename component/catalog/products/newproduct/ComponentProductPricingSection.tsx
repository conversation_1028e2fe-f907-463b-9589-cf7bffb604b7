'use client';

import React, { useEffect } from 'react';
import { DollarSign, Package, Layers, Percent } from 'lucide-react';
import { Control, UseFormRegister, UseFormWatch, useWatch, useFormContext } from 'react-hook-form';
import { ProductFormData } from '@/types/component/catalog/TypeProduct';

interface PricingSectionProps {
    control: Control<ProductFormData>;
    register: UseFormRegister<ProductFormData>;
    watch: UseFormWatch<ProductFormData>;
}

const currencyOptions = [
    { value: 'USD', label: '$' },
    { value: 'INR', label: '₹' },
    { value: 'AED', label: 'د.إ' }
];

const ComponentProductPricingSection: React.FC<PricingSectionProps> = ({ control, register, watch }) => {
    const { setValue } = useFormContext<ProductFormData>();
    const currency = watch('pricing.listPrice.currency') || 'USD';
    const costPrice = parseFloat(watch('pricing.costPrice.value') as any) || 0;
    const listPrice = parseFloat(watch('pricing.listPrice.value') as any) || 0;
    const discount = parseFloat(watch('pricing.discounts.0.discountValue.percentage') as any) || 0;
    const sellingPrice = parseFloat(watch('pricing.sellingPrice.value') as any) || 0;

    // Calculate margin percentage
    const marginPercentage = costPrice > 0 ? ((sellingPrice - costPrice) / sellingPrice) * 100 : 100;

    // Update margin in form data
    useEffect(() => {
        setValue('pricing.margin.percentage', marginPercentage);
        setValue('pricing.margin.absoluteAmount.value', sellingPrice - costPrice);
        setValue('pricing.margin.absoluteAmount.currency', currency);
    }, [setValue, marginPercentage, sellingPrice, costPrice, currency]);

    // Sync currency across all price fields
    useEffect(() => {
        setValue('pricing.costPrice.currency', currency);
        setValue('pricing.sellingPrice.currency', currency);
        setValue('pricing.discounts.0.discountValue.amount.currency', currency);
    }, [setValue, currency]);

    useEffect(() => {
        setValue('pricing.discounts.0.discountValue.amount.value', listPrice * (discount / 100));
    }, [discount, setValue])

    return (
        <div className="flex flex-col space-y-4">
            {/* List Price */}
            <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">List Price</label>
                <div className="flex relative items-center">
                    <DollarSign className="absolute left-2 h-4 w-4 text-gray-400 pointer-events-none" />
                    <input
                        type="number"
                        {...register('pricing.listPrice.value', {
                            setValueAs: (value) => parseFloat(value) || 0
                        })}
                        placeholder="List Price"
                        className="w-full text-sm p-2 pl-8 border-r-0 border border-gray-300 rounded-l-lg"
                    />
                    <select
                        {...register('pricing.listPrice.currency')}
                        className="text-sm p-2 border-l-0 border border-gray-300 rounded-r-lg bg-gray-50"
                    >
                        {currencyOptions.map(opt => (
                            <option key={opt.value} value={opt.value}>{opt.label}</option>
                        ))}
                    </select>
                </div>
            </div>

            {/* Cost Price */}
            <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Cost Price</label>
                <div className="flex relative items-center">
                    <DollarSign className="absolute left-2 h-4 w-4 text-gray-400 pointer-events-none" />
                    <input
                        type="number"
                        {...register('pricing.costPrice.value', {
                            setValueAs: (value) => parseFloat(value) || 0
                        })}
                        placeholder="Cost Price"
                        className="w-full text-sm p-2 pl-8 border-r-0 border border-gray-300 rounded-l-lg"
                    />
                    <select
                        value={currency}
                        disabled
                        className="text-sm p-2 border-l-0 border border-gray-300 rounded-r-lg bg-gray-50 disabled:bg-gray-100 disabled:cursor-not-allowed"
                    >
                        {currencyOptions.map(opt => (
                            <option key={opt.value} value={opt.value}>{opt.label}</option>
                        ))}
                    </select>
                </div>
            </div>

            {/* Quantity */}
            <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                    Quantity
                </label>
                <div className="flex relative items-center">
                    <Package className="absolute left-2 h-4 w-4 text-gray-400 pointer-events-none" />
                    <input
                        disabled
                        type="number"
                        {...register('pricing.unit.unit')}
                        value={1}
                        className="w-full text-sm p-2 pl-8 border-r-0 border border-gray-300 rounded-l-lg disabled:bg-gray-100 disabled:cursor-not-allowed"
                        tabIndex={-1}
                        aria-label="Quantity (always 1)"
                    />
                    <Layers className="absolute right-2 w-4 h-4 text-gray-400 pointer-events-none" />
                    <input
                        {...register('pricing.unit.unitType')}
                        placeholder="Unit (e.g. pax, piece)"
                        className="text-sm p-2 border-l-0 border border-gray-300 rounded-r-lg"
                        aria-label="Quantity Unit"
                    />
                </div>
            </div>

            {/* Discount */}
            <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Discount</label>
                <div className="relative flex items-center">
                    <Percent className="absolute left-2 h-4 w-4 text-gray-400 pointer-events-none" />
                    <input
                        type="number"
                        {...register('pricing.discounts.0.discountValue.percentage', {
                            setValueAs: (value) => parseFloat(value) || 0
                        })}
                        placeholder="Discount"
                        className="w-full text-sm p-2 pl-8 border border-gray-300 rounded-lg"
                    />
                </div>
            </div>

            {/* Selling Price (computed) */}
            <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Final Selling Price</label>
                <div className="flex items-center relative">
                    <div className="relative w-full">
                        <span className="absolute right-3 top-1/2 -translate-y-1/2 text-sm mr-2 text-gray-500 pointer-events-none">
                            {currencyOptions.find(c => c.value === currency)?.label}
                        </span>
                        <input
                            type="number"
                            {...register('pricing.sellingPrice.value')}
                            readOnly
                            tabIndex={-1}
                            className="w-full text-sm p-2 pr-10 border border-gray-300 rounded-lg bg-gray-50"
                        />
                    </div>
                </div>
            </div>

            {/* Margin (computed) */}
            <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Margin</label>
                <div className="flex items-center relative">
                    <div className="relative w-full">
                        <span className="absolute right-3 top-1/2 -translate-y-1/2 text-sm mr-2 text-gray-500 pointer-events-none">
                            %
                        </span>
                        <input
                            type="number"
                            value={marginPercentage.toFixed(2)}
                            readOnly
                            tabIndex={-1}
                            className="w-full text-sm p-2 pr-10 border border-gray-300 rounded-lg bg-gray-50"
                        />
                    </div>
                </div>
            </div>
        </div>
    );
};

export default ComponentProductPricingSection; 