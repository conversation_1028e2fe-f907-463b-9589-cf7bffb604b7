'use client';

import React, { useEffect, useState, useMemo } from 'react';
import { Briefcase, ChevronRight, Plus, Search } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { ApolloError } from '@apollo/client';
import { MasterProduct, ProductStatus, useGetAllProductsQuery } from '@/lib/graphql/types/generated/hooks';
import PagePagination from '@/component/pagination/ComponentPagePagination';
import ComponentLoading from '@/component/common/ComponentLoading';
import ComponentNote from '@/component/common/ComponentNote';
import { getUserFriendlyErrorMessage } from '@/lib/graphql/utils/errorHandling';


const ComponentProducts: React.FC = () => {
    const router = useRouter();
    const { data, loading, error: queryError } = useGetAllProductsQuery(
        {
            variables: {
                status: ProductStatus.Active
            }
        }
    )

    const [masterProducts, setMasterProducts] = useState<MasterProduct[]>([]);
    const [paginatedProducts, setPaginatedProducts] = useState<MasterProduct[]>([]);

    const [isError, setIsError] = useState<Boolean>(false);
    const [isLoading, setIsLoading] = useState<Boolean>(false);

    const [searchQuery, setSearchQuery] = useState('');
    const [currentPage, setCurrentPage] = useState(1);


    const recordsPerPage = 10;

    const filteredProducts = useMemo(() => {
        if (!searchQuery.trim()) return masterProducts;
        const query = searchQuery.toLowerCase().trim();
        return masterProducts.filter(product =>
            Object.values(product).some(value =>
                String(value).toLowerCase().includes(query)
            )
        );
    }, [searchQuery, masterProducts]);

    const totalPages = Math.ceil(filteredProducts.length / recordsPerPage);

    useEffect(() => {
        const paginatedProducts = filteredProducts.slice(
            (currentPage - 1) * recordsPerPage,
            currentPage * recordsPerPage
        );
        setPaginatedProducts(paginatedProducts);
    }, [filteredProducts, currentPage]);

    useEffect(() => {
        if (loading) {
            setIsLoading(true)
            return;
        }

        if (queryError) {
            setIsError(true);
            setIsLoading(false);
            return;
        }

        if (data) {
            setIsError(false);
            setIsLoading(false);
            setMasterProducts(data.masterProductsGet as MasterProduct[]);
        }
    }, [loading, queryError])


    return (
        <>
            <div className="space-y-6 px-1" >
                <div className="flex items-center justify-between">
                    <div className="flex flex-col gap-1">
                        <h2 className="text-2xl font-bold text-gray-900">Products</h2>
                        <p className="text-sm text-gray-600">
                            Manage your products and services
                        </p>
                    </div>
                </div>

                <div className="flex items-center justify-between rounded-lg mt-8 bg-white p-4 border border-gray-200" >
                    <div className="relative flex-1 max-w-lg">
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <Search className="h-5 w-5 text-gray-400" />
                        </div>
                        <input
                            type="text"
                            placeholder="Search products..."
                            className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                            value={searchQuery}
                            onChange={(e) => setSearchQuery(e.target.value)}
                        />
                    </div>
                    <button
                        onClick={() => router.push('/catalog/products/new')}
                        className="ml-4 px-4 py-2 bg-primary text-white rounded-lg hover:bg-opacity-90 cursor-pointer transition-colors flex items-center"
                    >
                        <Plus className="h-5 w-5 mr-2" />
                        New Product
                    </button>
                </div>

                {isLoading && (
                    <ComponentLoading
                        message="Loading products..."
                        className="min-h-[400px]"
                    />
                )}

                {isError && (
                    <div className="flex flex-col items-center justify-center min-h-[400px]">
                        <ComponentNote isError>
                            {getUserFriendlyErrorMessage(queryError as ApolloError)}
                        </ComponentNote>
                    </div>
                )}

                {!isLoading && !isError && paginatedProducts.length > 0 && (
                    <>
                        <div className="grid grid-cols-1 md:grid-cols-4 lg:grid-cols-5 gap-6">
                            {paginatedProducts.map((product) => (
                                <div
                                    key={product.id}
                                    className="bg-white max-h-80 rounded-lg border border-gray-200 cursor-pointer transition-all duration-200 hover:shadow-lg hover:border-primary"
                                    onClick={() => router.push(`/catalog/products/new?mode=edit&id=${product.id}`)}
                                >
                                    <div className="space-y-4 ">
                                        <div className="h-40 flex items-center justify-center bg-gray-100">
                                            <Briefcase className="h-16 w-16 text-gray-400" />
                                        </div>
                                        <div className="px-2 mb-2 flex flex-col justify-between">
                                            <div>
                                                <h3
                                                    className="text-lg font-semibold text-gray-900 truncate"
                                                    title={product.name}
                                                >
                                                    {product.name}
                                                </h3>
                                                <p
                                                    className="text-sm text-gray-600 mt-1 overflow-hidden text-ellipsis whitespace-nowrap"
                                                    style={{ maxWidth: '100%' }}
                                                    title={product.description || ''}
                                                >
                                                    {product.description}
                                                </p>
                                            </div>
                                            <div className="flex items-center justify-between mt-2">
                                                <span className="text-primary font-medium truncate">
                                                    {product.pricing?.sellingPrice?.value} {product.pricing?.sellingPrice?.currency} / {product.pricing?.unit?.unitType}
                                                </span>
                                                <span className="text-sm text-gray-500 flex items-center gap-1">
                                                    Details <ChevronRight className="h-4 w-4" />
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            ))}
                        </div>

                        {totalPages > 1 && paginatedProducts.length > 0 && (
                            <div className="flex justify-end items-center p-4">
                                <PagePagination
                                    currentPage={currentPage}
                                    totalPages={totalPages}
                                    onPageChange={setCurrentPage}
                                />
                            </div>
                        )}
                    </>
                )}
            </div >
        </>
    );
};

export default ComponentProducts; 