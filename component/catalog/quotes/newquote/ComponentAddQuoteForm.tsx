'use client';

import React, { useEffect, useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { ArrowLeft, Save, Send } from 'lucide-react';
import ComponentLoading from '@/component/common/ComponentLoading';

const ComponentAddQuoteForm: React.FC = () => {
    const router = useRouter();
    const searchParams = useSearchParams();
    const mode = searchParams.get('mode') || 'new';
    const quoteId = searchParams.get('id');
    
    const [isLoading, setIsLoading] = useState(false);
    const [isEditMode, setIsEditMode] = useState(mode === 'edit');

    useEffect(() => {
        if (mode === 'edit' && quoteId) {
            setIsEditMode(true);
            setIsLoading(true);
            // TODO: Load quote data for editing
            setTimeout(() => {
                setIsLoading(false);
            }, 1000);
        }
    }, [mode, quoteId]);

    const handleBack = () => {
        router.push('/catalog/quotes');
    };

    const handleSave = () => {
        // TODO: Implement save functionality
        console.log('Save quote');
    };

    const handleSend = () => {
        // TODO: Implement send functionality
        console.log('Send quote');
    };

    if (isLoading) {
        return (
            <div className="bg-white rounded-lg border border-gray-200">
                <ComponentLoading
                    message={isEditMode ? "Loading quote..." : "Preparing form..."}
                    className="min-h-[400px]"
                />
            </div>
        );
    }

    return (
        <div className="space-y-6">
            {/* Header */}
            <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                    <button
                        onClick={handleBack}
                        className="p-2 text-gray-600 hover:text-gray-800 transition-colors"
                    >
                        <ArrowLeft className="h-6 w-6" />
                    </button>
                    <div>
                        <h1 className="text-2xl font-bold text-gray-900">
                            {isEditMode ? `Edit Quote ${quoteId}` : 'Create New Quote'}
                        </h1>
                        <p className="text-sm text-gray-600">
                            {isEditMode ? 'Update quote details and send to customer' : 'Create a new quote for your customer'}
                        </p>
                    </div>
                </div>
                <div className="flex items-center space-x-3">
                    <button
                        onClick={handleSave}
                        className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors flex items-center gap-2"
                    >
                        <Save className="h-4 w-4" />
                        Save Draft
                    </button>
                    <button
                        onClick={handleSend}
                        className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-opacity-90 transition-colors flex items-center gap-2"
                    >
                        <Send className="h-4 w-4" />
                        Send Quote
                    </button>
                </div>
            </div>

            {/* Form Content */}
            <div className="bg-white rounded-lg border border-gray-200 p-6">
                <div className="space-y-6">
                    {/* Customer Selection */}
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                            Customer
                        </label>
                        <select className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                            <option value="">Select a customer...</option>
                            <option value="1">Acme Corporation</option>
                            <option value="2">Tech Solutions Inc</option>
                            <option value="3">Global Enterprises</option>
                        </select>
                    </div>

                    {/* Quote Details */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                Quote Number
                            </label>
                            <input
                                type="text"
                                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                                placeholder="Auto-generated"
                                disabled
                            />
                        </div>
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                Expiry Date
                            </label>
                            <input
                                type="date"
                                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                            />
                        </div>
                    </div>

                    {/* Products Section */}
                    <div>
                        <div className="flex items-center justify-between mb-4">
                            <h3 className="text-lg font-medium text-gray-900">Products & Services</h3>
                            <button className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-opacity-90 transition-colors">
                                Add Product
                            </button>
                        </div>
                        <div className="border border-gray-200 rounded-lg p-4 text-center text-gray-500">
                            No products added yet. Click "Add Product" to get started.
                        </div>
                    </div>

                    {/* Quote Summary */}
                    <div className="border-t pt-6">
                        <h3 className="text-lg font-medium text-gray-900 mb-4">Quote Summary</h3>
                        <div className="bg-gray-50 rounded-lg p-4">
                            <div className="flex justify-between items-center">
                                <span className="text-sm text-gray-600">Subtotal:</span>
                                <span className="text-sm font-medium">$0.00</span>
                            </div>
                            <div className="flex justify-between items-center mt-2">
                                <span className="text-sm text-gray-600">Tax:</span>
                                <span className="text-sm font-medium">$0.00</span>
                            </div>
                            <div className="flex justify-between items-center mt-2 pt-2 border-t border-gray-200">
                                <span className="text-lg font-bold text-gray-900">Total:</span>
                                <span className="text-lg font-bold text-gray-900">$0.00</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default ComponentAddQuoteForm;
