import React from 'react';
import { QuotesControlsProps } from '@/types/component/quotes/TypeQuote';

const QuotesControls: React.FC<QuotesControlsProps> = ({ searchQuery, onSearch, onAddQuote }) => (
    <div className="p-4 rounded-md bg-white border border-gray-200">
        <div className="flex flex-row justify-between items-center">
            <div className="relative">
                <input
                    type="text"
                    placeholder="Search quotes..."
                    className="pl-4 pr-10 py-2 border border-gray-300 rounded-lg"
                    value={searchQuery}
                    onChange={onSearch}
                />
                <svg className="absolute right-3 top-2.5 h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
            </div>
            <button
                onClick={onAddQuote}
                className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-opacity-90 transition-colors flex items-center gap-2">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clipRule="evenodd" />
                </svg>
                Add New Quote
            </button>
        </div>
    </div>
);

export default QuotesControls;
