import React, { useState } from 'react';
import { FileText, Tag, Info, Upload, Trash, Trash2, Plus, PlusCircle } from 'lucide-react';

export type DocumentInput = {
    file?: File | null;
    id?: string;
    description?: string;
    tags?: string[];
};

type Props = {
    value?: DocumentInput[];
    onDocumentsChange?: (docs: DocumentInput[]) => void;
    error?: string;
};

const defaultDocument: DocumentInput = {};

const ComponentDocumentUpload: React.FC<Props> = ({ value = [], onDocumentsChange, error }) => {
    const [documents, setDocuments] = useState<DocumentInput[]>(value);

    const handleAddDocument = () => {
        const newDocs = [...documents, { ...defaultDocument }];
        setDocuments(newDocs);
        onDocumentsChange?.(newDocs);
    };
    const handleRemoveDocument = (idx: number) => {
        const newDocs = documents.filter((_, i) => i !== idx);
        setDocuments(newDocs);
        onDocumentsChange?.(newDocs);
    };
    const handleDocumentChange = (idx: number, doc: DocumentInput | null) => {
        const newDocs = [...documents];
        newDocs[idx] = doc || {};
        setDocuments(newDocs);
        onDocumentsChange?.(newDocs);
    };

    // Single document input UI
    const renderDocumentInput = (doc: DocumentInput, idx: number) => (
        <div
            key={idx}
            className="relative border border-gray-100 rounded-lg p-4 flex flex-col min-h-[120px]"
        >
            <div className="space-y-3 mt-2">
                <label className="block relative">
                    <label className="flex items-center gap-2 cursor-pointer w-full">
                        <span className="flex items-center justify-center bg-primary hover:opacity-90 text-white rounded-lg p-2 mt-2 w-full">
                            <Upload className="h-5 w-5 mr-2" />
                            <span className="truncate text-sm">
                                {doc?.file?.name
                                    ? doc.file.name
                                    : <span className="opacity-70">Upload document</span>
                                }
                            </span>
                            <input
                                type="file"
                                onChange={e => {
                                    if (e.target.files && e.target.files[0]) {
                                        handleDocumentChange(idx, { ...doc, file: e.target.files[0] });
                                    }
                                }}
                                className="hidden"
                                aria-label="Upload document"
                            />
                        </span>
                    </label>
                </label>
                <label className="block relative">
                    <span className="absolute left-2 top-2 text-gray-400 pointer-events-none">
                        <Info className="h-4 w-4" />
                    </span>
                    <input
                        type="text"
                        placeholder="Description (optional)"
                        value={doc?.description || ''}
                        onChange={e => handleDocumentChange(idx, { ...doc, description: e.target.value })}
                        className="w-full p-2 pl-8 border border-gray-300 rounded-lg text-sm"
                    />
                </label>
                <label className="block relative">
                    <span className="absolute left-2 top-2 text-gray-400 pointer-events-none">
                        <Tag className="h-4 w-4" />
                    </span>
                    <input
                        type="text"
                        placeholder="Tags (comma separated)"
                        value={doc?.tags?.join(', ') || ''}
                        onChange={e => {
                            const tags = e.target.value.split(',').map(tag => tag.trim()).filter(Boolean);
                            handleDocumentChange(idx, { ...doc, tags });
                        }}
                        className="w-full p-2 pl-8 border border-gray-300 rounded-lg text-sm"
                    />
                </label>
                {error && <div className="text-red-500 text-xs mt-1">{error}</div>}
            </div>
            <button
                type="button"
                onClick={() => handleRemoveDocument(idx)}
                className="mt-2 bg-red-500/30 text-red-500 hover:bg-opacity-90 cursor-pointer rounded-md p-1 transition-colors w-full"
                aria-label="Remove Document"
            >
                <div className='flex items-center gap-2 justify-center'>
                    <Trash2 className='h-3 w-3' /> Delete
                </div>
            </button>
        </div>
    );

    return (
        <div>
            <div className="flex  gap-2 mb-2 items-center">
                <h3 className="text-lg font-semibold text-gray-900">Documents</h3>
                <button type="button" onClick={handleAddDocument} className="text-white hover:bg-opacity-90 text-xs bg-primary p-1 rounded-full cursor-pointer"><Plus className='h-4 w-4' /></button>
            </div>
            <div className="text-gray-500 text-sm mb-4">Upload product-related documents (e.g., spec sheets, images, manuals).</div>
            <div className={documents.length > 0 ? "grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4" : ""}>
                {documents.length > 0 ? (
                    documents.map((doc, idx) => renderDocumentInput(doc, idx))
                ) : (
                    <div className="border border-dashed border-gray-300 rounded-lg p-4 text-center text-gray-400 col-span-4">
                        No documents added yet.
                    </div>
                )}
            </div>
        </div>
    );
};

export default ComponentDocumentUpload; 