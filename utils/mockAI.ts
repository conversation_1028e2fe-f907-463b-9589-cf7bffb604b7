const responses: { [key: string]: string[] } = {
    greeting: [
        "Hello! How can I assist you today?",
        "Hi there! What can I help you with?",
        "Welcome! I'm here to help you with your business. What's on your mind?",
    ],
    farewell: [
        "Goodbye! Have a great day!",
        "See you later! Feel free to come back if you have more questions.",
        "<PERSON><PERSON><PERSON>! Don't hesitate to reach out again.",
    ],
    help: [
        "I can help you with a variety of tasks, such as managing customers, tracking services, and providing insights into your business.",
        "You can ask me to create a new customer, find a service, or show you a summary of your monthly revenue.",
        "I'm here to streamline your workflow. Try asking something like 'Show me all active customers' or 'What are my top-selling services?'.",
    ],
    "new customer": [
        "To create a new customer, I'll need some basic information. What is the customer's name?",
        "I can help with that. Let's start with the new customer's name and email address.",
    ],
    "code suggestion": [
        "Of course. Here is a code snippet to create a responsive button with Tailwind CSS:\n```html\n<button class=\"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded md:w-auto w-full\">Click me</button>\n```",
        "To fetch data in a React component, you can use the `useEffect` hook. Here's an example:\n```javascript\nimport React, { useState, useEffect } from 'react';\n\nfunction DataFetcher() {\n  const [data, setData] = useState(null);\n\n  useEffect(() => {\n    fetch('https://api.example.com/data')\n      .then(response => response.json())\n      .then(data => setData(data));\n  }, []);\n\n  return <div>{data ? JSON.stringify(data) : 'Loading...'}</div>;\n}\n```",
    ],
    default: [
        "I'm not sure I understand. Could you please rephrase that?",
        "That's an interesting question. Let me think about that.",
        "I'm still learning, but I'll do my best to help.",
        "Let's try something else. What would you like to do?",
    ],
};

const keywords: { [key: string]: string[] } = {
    greeting: ["hello", "hi", "hey"],
    farewell: ["bye", "goodbye", "see you"],
    help: ["help", "assist", "what can you do"],
    "new customer": ["new customer", "add customer", "create customer"],
    "code suggestion": ["code", "snippet", "javascript", "react", "tailwind"],
};

const getResponseType = (message: string): string => {
    const lowerCaseMessage = message.toLowerCase();
    for (const type in keywords) {
        if (keywords[type].some(keyword => lowerCaseMessage.includes(keyword))) {
            return type;
        }
    }
    return "default";
};

export const getMockAIResponse = (message: string): Promise<string> => {
    return new Promise(resolve => {
        const responseType = getResponseType(message);
        const possibleResponses = responses[responseType];
        const response = possibleResponses[Math.floor(Math.random() * possibleResponses.length)];

        setTimeout(() => {
            resolve(response);
        }, Math.random() * 2000 + 1000); // Simulate 1-3 second delay
    });
}; 